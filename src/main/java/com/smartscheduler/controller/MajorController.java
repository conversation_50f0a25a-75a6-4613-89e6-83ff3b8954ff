package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Major;
import com.smartscheduler.service.MajorService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/majors")
public class MajorController {

    @Autowired
    private MajorService majorService;

    @GetMapping
    public ResponseEntity<List<Major>> getAllMajors() {
        List<Major> majors = majorService.getAllMajors();
        return new ResponseEntity<>(majors, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getMajorById(@PathVariable Long id) {
        return majorService.getMajorById(id)
                .map(major -> new ResponseEntity<>(major, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Major not found with id: " + id), HttpStatus.NOT_FOUND));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getMajorByCode(@PathVariable String code) {
        return majorService.getMajorByCode(code)
                .map(major -> new ResponseEntity<>(major, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Major not found with code: " + code), HttpStatus.NOT_FOUND));
    }
    
    @GetMapping("/department/{departmentId}")
    public ResponseEntity<List<Major>> getMajorsByDepartment(@PathVariable Long departmentId) {
        List<Major> majors = majorService.getMajorsByDepartment(departmentId);
        return new ResponseEntity<>(majors, HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createMajor(@Valid @RequestBody Major major) {
        if (majorService.existsByCode(major.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Major code is already taken!"));
        }
        
        Major createdMajor = majorService.createMajor(major);
        return new ResponseEntity<>(createdMajor, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateMajor(@PathVariable Long id, @Valid @RequestBody Major major) {
        try {
            Major updatedMajor = majorService.updateMajor(id, major);
            return new ResponseEntity<>(updatedMajor, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteMajor(@PathVariable Long id) {
        try {
            majorService.deleteMajor(id);
            return new ResponseEntity<>(new MessageResponse("Major deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
