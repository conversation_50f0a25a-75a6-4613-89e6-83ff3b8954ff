package com.smartscheduler.controller;

import com.smartscheduler.dto.MessageResponse;
import com.smartscheduler.model.Campus;
import com.smartscheduler.service.CampusService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/campuses")
public class CampusController {

    @Autowired
    private CampusService campusService;

    @GetMapping
    public ResponseEntity<List<Campus>> getAllCampuses() {
        List<Campus> campuses = campusService.getAllCampuses();
        return new ResponseEntity<>(campuses, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getCampusById(@PathVariable Long id) {
        return campusService.getCampusById(id)
                .map(campus -> new ResponseEntity<>(campus, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Campus not found with id: " + id), HttpStatus.NOT_FOUND));
    }

    @GetMapping("/code/{code}")
    public ResponseEntity<?> getCampusByCode(@PathVariable String code) {
        return campusService.getCampusByCode(code)
                .map(campus -> new ResponseEntity<>(campus, HttpStatus.OK))
                .orElse(new ResponseEntity<>(new MessageResponse("Campus not found with code: " + code), HttpStatus.NOT_FOUND));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createCampus(@Valid @RequestBody Campus campus) {
        if (campusService.existsByCode(campus.getCode())) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: Campus code is already taken!"));
        }
        
        Campus createdCampus = campusService.createCampus(campus);
        return new ResponseEntity<>(createdCampus, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateCampus(@PathVariable Long id, @Valid @RequestBody Campus campus) {
        try {
            Campus updatedCampus = campusService.updateCampus(id, campus);
            return new ResponseEntity<>(updatedCampus, HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteCampus(@PathVariable Long id) {
        try {
            campusService.deleteCampus(id);
            return new ResponseEntity<>(new MessageResponse("Campus deleted successfully"), HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(new MessageResponse(e.getMessage()), HttpStatus.NOT_FOUND);
        }
    }
}
